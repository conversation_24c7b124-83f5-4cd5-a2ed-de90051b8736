// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";
import "./IERC20.sol";

contract NeoNixBridge is Ownable {
    uint256 private swapCounter = 1;
    mapping (bytes32 => bytes32) private swapCompleted;
    mapping (bytes32 => bytes32) private swapCompletedHashed;
    bytes32[] private swaps;

    event SwapInProgress(bytes32 indexed swapID, address indexed sender, address indexed recipient, uint256 amountIn,
         address fromCoin, bytes32 toCoinHash, uint256 fromChain,
        uint256 toChain);

    event SwapCompleted(bytes32 indexed swapID, address indexed sender, address recipient,
        uint256 amountIn, uint256 amountOut, address fromCoin, address toCoin, uint256 fromChain,
        uint256 toChain, bytes32 indexed swapHash);

    function crossChainSwap(address recipient, uint256 amountIn, address fromCoin,
         uint256 toChain) external{
        uint256 fromChain = block.chainid;

        IERC20 token = IERC20(fromCoin);
        bool success = token.transferFrom(_msgSender(), address(this), amountIn);

        require(success, "NeoNixBridge: Unable to transfer tokens from your wallet.");

        bytes32 toCoinHash = keccak256(abi.encodePacked(token.name(), token.symbol()));

        bytes32 swapID = keccak256(abi.encodePacked(
                swapCounter,
                block.timestamp + block.difficulty +
                ((uint256(keccak256(abi.encodePacked(block.coinbase)))) / (block.timestamp)) +
                block.gaslimit +
                ((uint256(keccak256(abi.encodePacked(_msgSender())))) / (block.timestamp)) +
                block.number
        ));

        swapCounter++;

        swaps.push(swapID);

        emit SwapInProgress(swapID, _msgSender(), recipient, amountIn, fromCoin, toCoinHash,
            fromChain, toChain);
    }

    function completeCrossChainSwap(bytes32 swapID, address sender, address recipient, uint256 amountIn, uint256 amountOut,
        address fromCoin, address toCoin, uint256 fromChain, uint256 toChain, bytes32 swapHash) external onlyOwner{

        swapCompleted[swapID] = swapHash;
        swapCompletedHashed[swapHash] = swapID;

        emit SwapCompleted(swapID, sender, recipient, amountIn, amountOut,
            fromCoin, toCoin, fromChain, toChain, swapHash);
    }

    function getSwapIDFromHash(bytes32 _hash) public view returns(bytes32){
        return swapCompletedHashed[_hash];
    }

    function getSwapHashFromID(bytes32 _id) public view returns(bytes32){
        return swapCompleted[_id];
    }

    function getSwaps() public view returns(bytes32[] memory){
        return swaps;
    }

    function withdrawTokens(uint256 _amount, address _address) external onlyOwner(){
        IERC20 tokenContract = IERC20(_address);
        tokenContract.transfer(owner(), _amount);
    }

}