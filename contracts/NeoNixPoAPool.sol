// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "./IERC20.sol";


contract NeoNixPoAPool is Ownable {
    using SafeMath for uint256;
    uint256 private WEI = 1000000000000000000;


    constructor (){
    }

    function sendToken(address _tokenAddr, address payable _to, uint256 _amount) external onlyOwner(){
        IERC20(_tokenAddr).transfer(_to, _amount);
    }


    function sendCoin(address payable _to, uint256 _amount) external onlyOwner(){
        require(address(this).balance >= _amount, "PoAPool: invalid digits");
        _to.transfer(_amount);
    }

}
