// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "./ERC20LateInit.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
abstract contract ERC20LatePausable is ERC20LateInit, Pausable {
    /**
     * @dev See {ERC20-_beforeTokenTransfer}.
     *
     * Requirements:
     *
     * - the contract must not be paused.
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal virtual override {
        super._beforeTokenTransfer(from, to, amount);

        require(!paused(), "ERC20Pausable: token transfer while paused");
    }
}
