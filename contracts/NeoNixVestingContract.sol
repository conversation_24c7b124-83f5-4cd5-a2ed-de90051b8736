// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

contract NeoNixVestingContract is Ownable {
    using SafeMath for uint256;
    
    enum VestingCategory {
        ICO_INVESTORS,
        TEAM_ADVISORS,
        PARTNERSHIPS,
        EARLY_ADOPTERS,
        VALIDATORS_REWARDS,
        MARKETING_EXPANSION,
        TREASURY_RESERVE,
        LIQUIDITY_POOL
    }
    
    struct VestingSchedule {
        uint256 totalAmount;
        uint256 startTimestamp;
        uint256 cliffDuration;
        uint256 vestingDuration;
        uint256 monthlyReleasePercentage; // In basis points (1/100 of a percent)
        uint256 releasedAmount;
        bool isFlexible;
    }
    
    mapping(address => VestingSchedule) public vestingSchedules;
    mapping(address => VestingCategory) public addressCategory;
    mapping(VestingCategory => address) public categoryAddress;
    
    event VestingScheduleCreated(address beneficiary, VestingCategory category, uint256 amount);
    event TokensReleased(address beneficiary, uint256 amount);
    event FlexibleVestingReleased(address beneficiary, uint256 amount);
    event FundsDeposited(address sender, uint256 amount);
    
    // Allow contract to receive native coin
    receive() external payable {
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    function setCategoryAddress(VestingCategory _category, address _beneficiary) external onlyOwner {
        require(_beneficiary != address(0), "Invalid address");
        categoryAddress[_category] = _beneficiary;
        addressCategory[_beneficiary] = _category;
    }
    
    function createVestingSchedule(
        address _beneficiary,
        uint256 _amount,
        uint256 _startTimestamp,
        bool _isFlexible
    ) external onlyOwner {
        require(_beneficiary != address(0), "Invalid beneficiary address");
        require(_amount > 0, "Amount must be greater than 0");
        require(vestingSchedules[_beneficiary].totalAmount == 0, "Vesting schedule already exists");
        require(address(this).balance >= _amount, "Insufficient contract balance");
        
        VestingCategory category = addressCategory[_beneficiary];
        
        uint256 cliffDuration;
        uint256 vestingDuration;
        uint256 monthlyReleasePercentage;
        
        if (_isFlexible) {
            // Flexible schedules have no predefined parameters
            vestingSchedules[_beneficiary] = VestingSchedule({
                totalAmount: _amount,
                startTimestamp: _startTimestamp,
                cliffDuration: 0,
                vestingDuration: 0,
                monthlyReleasePercentage: 0,
                releasedAmount: 0,
                isFlexible: true
            });
        } else {
            // Set parameters based on category
            if (category == VestingCategory.ICO_INVESTORS) {
                cliffDuration = 0;
                vestingDuration = 36 * 30 days;
                monthlyReleasePercentage = 278; // 2.78% per month (100/36)
            } else if (category == VestingCategory.TEAM_ADVISORS) {
                cliffDuration = 60 * 30 days;
                vestingDuration = 60 * 30 days;
                monthlyReleasePercentage = 167; // 1.67% per month
            } else if (category == VestingCategory.PARTNERSHIPS) {
                cliffDuration = 12 * 30 days;
                vestingDuration = 36 * 30 days;
                monthlyReleasePercentage = 277; // 2.77% per month
            } else if (category == VestingCategory.MARKETING_EXPANSION) {
                cliffDuration = 24 * 30 days;
                vestingDuration = 48 * 30 days;
                monthlyReleasePercentage = 208; // 2.08% per month
            } else {
                revert("Invalid category for fixed vesting");
            }
            
            vestingSchedules[_beneficiary] = VestingSchedule({
                totalAmount: _amount,
                startTimestamp: _startTimestamp,
                cliffDuration: cliffDuration,
                vestingDuration: vestingDuration,
                monthlyReleasePercentage: monthlyReleasePercentage,
                releasedAmount: 0,
                isFlexible: false
            });
        }
        
        emit VestingScheduleCreated(_beneficiary, category, _amount);
    }
    
    function claimVest() external {
        address beneficiary = msg.sender;
        VestingSchedule storage schedule = vestingSchedules[beneficiary];
        
        require(schedule.totalAmount > 0, "No vesting schedule found");
        
        if (schedule.isFlexible) {
            revert("Flexible vesting requires owner approval");
        }
        
        uint256 releasableAmount = calculateReleasableAmount(beneficiary);
        require(releasableAmount > 0, "No tokens available for release");
        require(address(this).balance >= releasableAmount, "Insufficient contract balance");
        
        schedule.releasedAmount = schedule.releasedAmount.add(releasableAmount);
        
        (bool success, ) = payable(beneficiary).call{value: releasableAmount}("");
        require(success, "Native coin transfer failed");
        
        emit TokensReleased(beneficiary, releasableAmount);
    }
    
    function releaseFlexibleVesting(address _beneficiary, uint256 _amount) external onlyOwner {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        
        require(schedule.isFlexible, "Not a flexible vesting schedule");
        require(_amount > 0, "Amount must be greater than 0");
        require(schedule.releasedAmount.add(_amount) <= schedule.totalAmount, "Exceeds available tokens");
        require(address(this).balance >= _amount, "Insufficient contract balance");
        
        schedule.releasedAmount = schedule.releasedAmount.add(_amount);
        
        (bool success, ) = payable(_beneficiary).call{value: _amount}("");
        require(success, "Native coin transfer failed");
        
        emit FlexibleVestingReleased(_beneficiary, _amount);
    }
    
    function calculateReleasableAmount(address _beneficiary) public view returns (uint256) {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        
        if (schedule.totalAmount == 0 || schedule.isFlexible) {
            return 0;
        }
        
        if (block.timestamp < schedule.startTimestamp.add(schedule.cliffDuration)) {
            return 0;
        }
        
        if (schedule.releasedAmount >= schedule.totalAmount) {
            return 0;
        }
        
        uint256 elapsedTime = block.timestamp.sub(schedule.startTimestamp);
        uint256 elapsedMonths = elapsedTime.div(30 days);
        
        uint256 vestedAmount;
        
        if (elapsedTime >= schedule.vestingDuration) {
            vestedAmount = schedule.totalAmount;
        } else {
            vestedAmount = schedule.totalAmount.mul(schedule.monthlyReleasePercentage).mul(elapsedMonths).div(10000);
        }
        
        uint256 releasableAmount = vestedAmount.sub(schedule.releasedAmount);
        
        return releasableAmount;
    }
    
    function getVestingSchedule(address _beneficiary) external view returns (
        uint256 totalAmount,
        uint256 startTimestamp,
        uint256 cliffDuration,
        uint256 vestingDuration,
        uint256 monthlyReleasePercentage,
        uint256 releasedAmount,
        bool isFlexible
    ) {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        return (
            schedule.totalAmount,
            schedule.startTimestamp,
            schedule.cliffDuration,
            schedule.vestingDuration,
            schedule.monthlyReleasePercentage,
            schedule.releasedAmount,
            schedule.isFlexible
        );
    }
    
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Native coin transfer failed");
    }
    
    // Function to deposit funds to the contract
    function depositFunds() external payable onlyOwner {
        require(msg.value > 0, "Must send some funds");
        emit FundsDeposited(msg.sender, msg.value);
    }
}