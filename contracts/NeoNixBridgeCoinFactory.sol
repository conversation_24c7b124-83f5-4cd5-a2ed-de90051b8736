// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";
import "./BridgeCoin.sol";
import { Clones } from "@openzeppelin/contracts/proxy/Clones.sol";

contract NeoNixBridgeCoinFactory is Ownable {

    address private libraryAddress;
    address[] private bridgeCoins;
    mapping(bytes32 => address) private coinMap;

    event BridgeCoinCreated(string indexed name, string indexed symbol, address newBridgeCoin);

    constructor() {
    }

    function setLibraryAddress(address _libraryAddress) public onlyOwner {
        libraryAddress = _libraryAddress;
    }

    function getLibraryAddress() public view returns (address) {
        return libraryAddress;
    }

    function getBridgeCoins() public view returns (address[] memory) {
        return bridgeCoins;
    }

    function getBridgeCoin(uint256 _index) public view returns (address){
        return bridgeCoins[_index];
    }

    function getBridgeCoin(bytes32 _hash) public view returns (address){
        return coinMap[_hash];
    }

    function createBridgeCoin(string memory _name, string memory _symbol, uint256 _fee)
    public onlyOwner {
        address clone = Clones.clone(libraryAddress);
        BridgeCoin(clone).init(address(this),_name, _symbol, _fee, owner());
        bridgeCoins.push(clone);
        coinMap[keccak256(abi.encodePacked(_name, _symbol))] = clone;

        emit BridgeCoinCreated(_name, _symbol, clone);
    }

    function addBridgeCoin(string memory _name, string memory _symbol, address _coinAddr)
    public onlyOwner {
        bridgeCoins.push(_coinAddr);
        coinMap[keccak256(abi.encodePacked(_name, _symbol))] = _coinAddr;

        emit BridgeCoinCreated(_name, _symbol, _coinAddr);
    }

}
