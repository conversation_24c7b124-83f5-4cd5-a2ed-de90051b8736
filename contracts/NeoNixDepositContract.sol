// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "./IERC20.sol";


contract NeoNixDepositContract is Ownable {
    using SafeMath for uint256;
    IERC20 private USDT_TOKEN;
    uint256 private WEI = 1000000000000000000;
    uint256 private CURRENT_PRICE = 100000000000000000;
    //auths
    mapping (address => Authorization[]) authorizations;

    event ActionAuthorized(address indexed account, uint256 namespace, uint256 actionType, bytes32 data);
    event USDTDepositAccepted(address indexed _address, uint256 _amount, uint256 _tokens);

    struct Authorization{
        address _address;
        uint256 _namespace;
        uint256 _actionType;
        bytes32 _data;
    }

    //begin

    constructor (address usdtToken){
        USDT_TOKEN = IERC20(usdtToken);
    }


    function depositUSDT(uint256 _amount) external payable{
        require(USDT_TOKEN.allowance(_msgSender(), address(this)) >= _amount,"Stake: You need to allow transferring token");
        USDT_TOKEN.transferFrom(_msgSender(), address(this), _amount);
        uint256 _tokens = _amount.div(getCurrentPrice());
        emit USDTDepositAccepted(_msgSender(), _amount, _tokens);
    }

    function sendToken(address _tokenAddr, address payable _to, uint256 _amount) external onlyOwner(){
        IERC20(_tokenAddr).transfer(_to, _amount);
    }

    function getCurrentPrice() public view returns(uint256){
        return CURRENT_PRICE;
    }

    function sendCurrentPrice(uint256 _price) external onlyOwner(){
        CURRENT_PRICE = _price;
    }


    function sendCoin(address payable _to, uint256 _amount) external onlyOwner(){
        require(address(this).balance >= _amount, "Stake: invalid digits");
        _to.transfer(_amount);
    }

    function authorizeAction(uint256 _namespace, uint256 _actionType, bytes32 _data) external {
        authorizations[_msgSender()].push(Authorization(_msgSender(), _namespace, _actionType, _data));
        emit ActionAuthorized(_msgSender(), _namespace, _actionType, _data);
    }

    function getAuthorizations(address _of) public view returns(Authorization[] memory) {
        return authorizations[_of];
    }


    function clean(uint256 _amount) external onlyOwner(){
        require(address(this).balance > _amount, "Invalid digits");

        payable(owner()).transfer(_amount);
    }

    function getUSDToken() public view returns(address){
        return address(USDT_TOKEN);
    }

    function setUSDToken(address _addr) public onlyOwner{
        USDT_TOKEN = IERC20(_addr);
    }

}
