// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/access/Ownable.sol";

import "./ERC20LateInit.sol";
import "./ERC20LateMinterPauser.sol";

contract BridgeCoin is Ownable, ERC20LatePresetMinterPauser {
    address private factoryAddress;

    uint256 private fee; //like 100 = 1%
    uint256 private feeBP = 10000;
    bytes32 private hash;

    event BridgeCoinCreated(address indexed coin, string name, string symbol, bytes32 indexed hash);

    constructor(address _factoryAddress, string memory _name, string memory _symbol)
    ERC20LatePresetMinterPauser(_name, _symbol){

    }

    modifier factoryOrOwner(){
        require(factoryAddress == address(0) || _msgSender() == factoryAddress || owner() == _msgSender(), "Not enough permissions");
        _;
    }

    function init(address _factoryAddress, string memory _name, string memory _symbol, uint256 _fee, address newOwner)
    external factoryOrOwner{
        require(hash == 0, "BridgeCoin: Already initialized");
        fee = _fee;
        hash = keccak256(abi.encodePacked(_name, _symbol));
        setName(_name);
        setSymbol(_symbol);
        factoryAddress = _factoryAddress;
        _setupRole(MINTER_ROLE, factoryAddress);
        _setupRole(DEFAULT_ADMIN_ROLE, newOwner);
        _setupRole(MINTER_ROLE, newOwner);
        _setupRole(PAUSER_ROLE, newOwner);
        _transferOwnership(newOwner);
        emit BridgeCoinCreated(address(this), _name, _symbol, hash);
    }

    function getOwner() public view returns (address){
        return owner();
    }

    function setFactoryAddress(address _factoryAddress) public onlyOwner {
        factoryAddress = _factoryAddress;
    }

    function getFactoryAddress() public view returns (address) {
        return factoryAddress;
    }

    function getHash() public view returns (bytes32){
        return hash;
    }
    function getFee() public view returns (uint256){
        return fee;
    }
    function getFeeBP() public view returns (uint256){
        return feeBP;
    }

    function setFee(uint256 _fee) public onlyOwner{
        fee = _fee;
    }

    function setFeeBP(uint256 _feeBP) public onlyOwner{
        feeBP = _feeBP;
    }
}
