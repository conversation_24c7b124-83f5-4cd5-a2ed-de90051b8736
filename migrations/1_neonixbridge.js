const BridgeCoin = artifacts.require("BridgeCoin");
const NeoNixBridgeCoinFactory = artifacts.require("NeoNixBridgeCoinFactory");
const NeoNixBridge = artifacts.require("NeoNixBridge");

module.exports = async function(deployer) {
    // await deployer.deploy(NeoNixBridgeCoinFactory)
    // await deployer.deploy(BridgeCoin, NeoNixBridgeCoinFactory.address, 'Wrapped NNX', 'WNNX')
    // await deployer.deploy(NeoNixBridge)

    // let factory = await NeoNixBridgeCoinFactory.deployed()
    // await factory.setLibraryAddress(BridgeCoin.address);

};
