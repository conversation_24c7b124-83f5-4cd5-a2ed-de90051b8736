const NeoNixVestingContract = artifacts.require("NeoNixVestingContract");
const BridgeCoin = artifacts.require("BridgeCoin");
const MockERC20 = artifacts.require("MockERC20");

module.exports = async function(deployer, network, accounts) {
  const owner = accounts[0];

  const chainId = await web3.eth.getChainId();
  if(chainId != 989890){
    return;
  }
  await deployer.deploy(NeoNixVestingContract);
  console.log(`NeoNixVestingContract deployed`);

};