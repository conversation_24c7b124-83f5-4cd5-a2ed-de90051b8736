const BridgeCoin = artifacts.require("BridgeCoin");
const NeoNixBridgeCoinFactory = artifacts.require("NeoNixBridgeCoinFactory");
const NeoNixBridge = artifacts.require("NeoNixBridge");
const senderAddress = "0x1211432f1f6e3b4e7b17dd6f078958524d5eef71";

contract("BridgeCoin", (accounts)=>{
    it("should have deployed base WNNX Bridge Coin", async () =>{
        const coin = await BridgeCoin.deployed();
        const coinSymbol = await coin.symbol();
        assert.equal(coinSymbol, "WNNX", "Coin symbol doesn't match WNNX");
    })
})

contract("NeoNixBridge", async (accounts)=>{
    let bridgeCoinAddress;
    it("should have address of BridgeCoin as library address", async () =>{
        const factory = await NeoNixBridgeCoinFactory.deployed();
        const factoryAddress = await factory.getLibraryAddress();

        assert.equal(BridgeCoin.address, factoryAddress, "Factory address doesnt match bridge coin address");
    })

    it("should have 0 bridge coin address", async() =>{
        const factory = await NeoNixBridgeCoinFactory.deployed();
        const coins = await factory.getBridgeCoins()

        assert.equal(coins.length, 0, "Bridge coin length is not equal to 0");
    })

    it("should create a bridge coin clone", async() =>{
        const factory = await NeoNixBridgeCoinFactory.deployed();
        await factory.createBridgeCoin("Wrapped Ether", "WETH", 100,{from: senderAddress});

        const coins = await factory.getBridgeCoins();
        assert.equal(coins.length, 1, "Bridge coin length is not equal to 1");
    })

    it("should fail when reinitializing the bridge coin", async() =>{
        const factory = await NeoNixBridgeCoinFactory.deployed();
        const coins = await factory.getBridgeCoins();
        bridgeCoinAddress = coins[0];
        let bridgeCoin = await BridgeCoin.at(bridgeCoinAddress);
        try {
            await bridgeCoin.init(factory.address, "Temp name", "SYM", 100, senderAddress);
            assert.fail("Reinit possible");
        }
        catch(e){
            assert.include(e.message, "Already initialized");
        }

    })
    it("should queue a cross chain swap", async() =>{
        const bridge = await NeoNixBridge.deployed();
        const factory = await NeoNixBridgeCoinFactory.deployed();
        const coins = await factory.getBridgeCoins();
        const coin = coins[0];

        try {
            await bridge.crossChainSwap(accounts[1], web3.utils.toWei("1", "ether"), coin, 765) //765 - thoth
            assert.fail("Allowance wasn't given");
        }
        catch(e){
            assert.include(e.message, "insufficient allowance")
            const bridgeCoin = await BridgeCoin.at(coin);

            //transfer balance
            bridgeCoin.mint(senderAddress, web3.utils.toWei("1","ether"), {from: senderAddress});

            //give allowance
            await bridgeCoin.approve(bridge.address,web3.utils.toWei("1","ether"));

            const hash = await bridge.crossChainSwap(accounts[1], web3.utils.toWei("1", "ether"), coin, 765) //765 - thoth

            assert.equal(hash.logs[0].event,"SwapInProgress");
        }
    })
})