const NeoNixVestingContract = artifacts.require("NeoNixVestingContract");
const MockERC20 = artifacts.require("MockERC20");
const { time } = require('@openzeppelin/test-helpers');
const { web3 } = require('@openzeppelin/test-helpers/src/setup');

contract("NeoNixVestingContract", accounts => {
  const [owner, icoInvestor, teamMember, partner, earlyAdopter, validator, marketing, treasury, liquidity] = accounts;
  
  const MONTH = 30 * 24 * 60 * 60; // 30 days in seconds
  const TOKEN_AMOUNT = web3.utils.toWei("1000000", "ether"); // 1 million tokens
  const VESTING_AMOUNT = web3.utils.toWei("100000", "ether"); // 100k tokens per category
  
  let vestingContract;
  let tokenContract;
  let startTime;
  
  before(async () => {
    // Deploy mock token
    tokenContract = await MockERC20.new("Test Token", "TEST", TOKEN_AMOUNT);
    
    // Deploy vesting contract
    vestingContract = await NeoNixVestingContract.new(tokenContract.address);
    
    // Transfer tokens to vesting contract
    await tokenContract.transfer(vestingContract.address, TOKEN_AMOUNT);
    
    // Get current timestamp
    startTime = (await time.latest()).toNumber();
  });
  
  describe("Contract setup", () => {
    it("should have the correct token address", async () => {
      const tokenAddress = await vestingContract.token();
      assert.equal(tokenAddress, tokenContract.address, "Token address doesn't match");
    });
    
    it("should have the correct token balance", async () => {
      const balance = await tokenContract.balanceOf(vestingContract.address);
      assert.equal(balance.toString(), TOKEN_AMOUNT, "Contract balance doesn't match");
    });
  });
  
  describe("Category setup", () => {
    it("should set category addresses correctly", async () => {
      await vestingContract.setCategoryAddress(0, icoInvestor, { from: owner }); // ICO_INVESTORS
      await vestingContract.setCategoryAddress(1, teamMember, { from: owner }); // TEAM_ADVISORS
      await vestingContract.setCategoryAddress(2, partner, { from: owner }); // PARTNERSHIPS
      await vestingContract.setCategoryAddress(3, earlyAdopter, { from: owner }); // EARLY_ADOPTERS
      await vestingContract.setCategoryAddress(4, validator, { from: owner }); // VALIDATORS_REWARDS
      await vestingContract.setCategoryAddress(5, marketing, { from: owner }); // MARKETING_EXPANSION
      await vestingContract.setCategoryAddress(6, treasury, { from: owner }); // TREASURY_RESERVE
      await vestingContract.setCategoryAddress(7, liquidity, { from: owner }); // LIQUIDITY_POOL
      
      const icoCategory = await vestingContract.addressCategory(icoInvestor);
      assert.equal(icoCategory.toString(), "0", "ICO investor category doesn't match");
      
      const teamCategory = await vestingContract.addressCategory(teamMember);
      assert.equal(teamCategory.toString(), "1", "Team member category doesn't match");
    });
    
    it("should not allow non-owner to set category addresses", async () => {
      try {
        await vestingContract.setCategoryAddress(0, accounts[9], { from: accounts[9] });
        assert.fail("Non-owner was able to set category address");
      } catch (error) {
        assert(error.message.includes("Ownable: caller is not the owner"), "Wrong error message");
      }
    });
  });
  
  describe("Vesting schedule creation", () => {
    it("should create vesting schedules for fixed categories", async () => {
      // ICO Investors - linear monthly vesting
      await vestingContract.createVestingSchedule(
        icoInvestor, 
        VESTING_AMOUNT, 
        startTime, 
        false, 
        { from: owner }
      );
      
      // Team & Advisors - 60-month cliff, then 1.67% monthly
      await vestingContract.createVestingSchedule(
        teamMember, 
        VESTING_AMOUNT, 
        startTime, 
        false, 
        { from: owner }
      );
      
      // Partnerships - 12-month cliff, then 2.77% monthly
      await vestingContract.createVestingSchedule(
        partner, 
        VESTING_AMOUNT, 
        startTime, 
        false, 
        { from: owner }
      );
      
      // Marketing & Expansion - 24-month cliff, then 2.08% monthly
      await vestingContract.createVestingSchedule(
        marketing, 
        VESTING_AMOUNT, 
        startTime, 
        false, 
        { from: owner }
      );
      
      const icoSchedule = await vestingContract.getVestingSchedule(icoInvestor);
      assert.equal(icoSchedule.totalAmount.toString(), VESTING_AMOUNT, "ICO investor amount doesn't match");
      assert.equal(icoSchedule.isFlexible, false, "ICO investor schedule should not be flexible");
      
      const teamSchedule = await vestingContract.getVestingSchedule(teamMember);
      assert.equal(teamSchedule.cliffDuration.toString(), (60 * MONTH).toString(), "Team member cliff duration doesn't match");
    });
    
    it("should create flexible vesting schedules", async () => {
      // Early adopters - Flexible
      await vestingContract.createVestingSchedule(
        earlyAdopter, 
        VESTING_AMOUNT, 
        startTime, 
        true, 
        { from: owner }
      );
      
      // Validators - Flexible
      await vestingContract.createVestingSchedule(
        validator, 
        VESTING_AMOUNT, 
        startTime, 
        true, 
        { from: owner }
      );
      
      // Treasury - Flexible
      await vestingContract.createVestingSchedule(
        treasury, 
        VESTING_AMOUNT, 
        startTime, 
        true, 
        { from: owner }
      );
      
      // Liquidity - Flexible
      await vestingContract.createVestingSchedule(
        liquidity, 
        VESTING_AMOUNT, 
        startTime, 
        true, 
        { from: owner }
      );
      
      const earlyAdopterSchedule = await vestingContract.getVestingSchedule(earlyAdopter);
      assert.equal(earlyAdopterSchedule.isFlexible, true, "Early adopter schedule should be flexible");
    });
    
    it("should not allow creating duplicate vesting schedules", async () => {
      try {
        await vestingContract.createVestingSchedule(
          icoInvestor, 
          VESTING_AMOUNT, 
          startTime, 
          false, 
          { from: owner }
        );
        assert.fail("Was able to create duplicate vesting schedule");
      } catch (error) {
        assert(error.message.includes("Vesting schedule already exists"), "Wrong error message");
      }
    });
  });
  
  describe("Vesting calculations and claims", () => {
    it("should calculate zero releasable amount before cliff period", async () => {
      // For Team member with 60-month cliff
      const releasableAmount = await vestingContract.calculateReleasableAmount(teamMember);
      assert.equal(releasableAmount.toString(), "0", "Releasable amount should be zero before cliff");
    });
    
    it("should calculate correct releasable amount for ICO investors (no cliff)", async () => {
      // Advance time by 1 month
      await time.increase(MONTH);
      
      const releasableAmount = await vestingContract.calculateReleasableAmount(icoInvestor);
      const expectedAmount = web3.utils.toBN(VESTING_AMOUNT).mul(web3.utils.toBN(278)).div(web3.utils.toBN(10000));
      assert.equal(releasableAmount.toString(), expectedAmount.toString(), "Releasable amount doesn't match expected");
    });
    
    it("should allow ICO investor to claim vested tokens", async () => {
      const initialBalance = await tokenContract.balanceOf(icoInvestor);
      
      await vestingContract.claimVest({ from: icoInvestor });
      
      const newBalance = await tokenContract.balanceOf(icoInvestor);
      const releasedAmount = newBalance.sub(initialBalance);
      
      assert(releasedAmount.gt(web3.utils.toBN(0)), "No tokens were released");
      
      // Check that released amount is tracked
      const schedule = await vestingContract.getVestingSchedule(icoInvestor);
      assert.equal(schedule.releasedAmount.toString(), releasedAmount.toString(), "Released amount not tracked correctly");
    });
    
    it("should not allow claiming twice in the same period", async () => {
      try {
        await vestingContract.claimVest({ from: icoInvestor });
        assert.fail("Was able to claim twice in the same period");
      } catch (error) {
        assert(error.message.includes("No tokens available for release"), "Wrong error message");
      }
    });
    
    it("should not allow claiming from flexible vesting schedule", async () => {
      try {
        await vestingContract.claimVest({ from: earlyAdopter });
        assert.fail("Was able to claim from flexible vesting schedule");
      } catch (error) {
        assert(error.message.includes("Flexible vesting requires owner approval"), "Wrong error message");
      }
    });
    
    it("should allow owner to release flexible vesting", async () => {
      const releaseAmount = web3.utils.toWei("10000", "ether"); // 10k tokens
      const initialBalance = await tokenContract.balanceOf(earlyAdopter);
      
      await vestingContract.releaseFlexibleVesting(earlyAdopter, releaseAmount, { from: owner });
      
      const newBalance = await tokenContract.balanceOf(earlyAdopter);
      const releasedAmount = newBalance.sub(initialBalance);
      
      assert.equal(releasedAmount.toString(), releaseAmount, "Released amount doesn't match");
    });
    
    it("should not allow non-owner to release flexible vesting", async () => {
      try {
        await vestingContract.releaseFlexibleVesting(
          validator, 
          web3.utils.toWei("10000", "ether"), 
          { from: accounts[9] }
        );
        assert.fail("Non-owner was able to release flexible vesting");
      } catch (error) {
        assert(error.message.includes("Ownable: caller is not the owner"), "Wrong error message");
      }
    });
  });
  
  describe("Long-term vesting", () => {
    it("should release correct amounts after cliff periods", async () => {
      // Advance time by 12 months to pass partnership cliff
      await time.increase(12 * MONTH);
      
      const partnerReleasable = await vestingContract.calculateReleasableAmount(partner);
      assert(partnerReleasable.gt(web3.utils.toBN(0)), "Partner should have releasable tokens after cliff");
      
      // Advance time by 12 more months to pass marketing cliff
      await time.increase(12 * MONTH);
      
      const marketingReleasable = await vestingContract.calculateReleasableAmount(marketing);
      assert(marketingReleasable.gt(web3.utils.toBN(0)), "Marketing should have releasable tokens after cliff");
      
      // Advance time by 36 more months to pass team cliff
      await time.increase(36 * MONTH);
      
      const teamReleasable = await vestingContract.calculateReleasableAmount(teamMember);
      assert(teamReleasable.gt(web3.utils.toBN(0)), "Team member should have releasable tokens after cliff");
    });
    
    it("should release full amount after vesting period", async () => {
      // Advance time by 60 more months to complete all vesting
      await time.increase(60 * MONTH);
      
      // ICO investor should have all tokens vested
      const icoReleasable = await vestingContract.calculateReleasableAmount(icoInvestor);
      const icoSchedule = await vestingContract.getVestingSchedule(icoInvestor);
      const icoExpected = web3.utils.toBN(VESTING_AMOUNT).sub(icoSchedule.releasedAmount);
      
      assert.equal(icoReleasable.toString(), icoExpected.toString(), "ICO investor should have all remaining tokens vested");
      
      // Claim the tokens
      await vestingContract.claimVest({ from: icoInvestor });
      
      // Check that all tokens were released
      const finalSchedule = await vestingContract.getVestingSchedule(icoInvestor);
      assert.equal(finalSchedule.releasedAmount.toString(), VESTING_AMOUNT, "All tokens should be released");
    });
  });
  
  describe("Emergency functions", () => {
    it("should allow owner to withdraw tokens in emergency", async () => {
      const initialBalance = await tokenContract.balanceOf(owner);
      const contractBalance = await tokenContract.balanceOf(vestingContract.address);
      
      await vestingContract.emergencyWithdraw({ from: owner });
      
      const newBalance = await tokenContract.balanceOf(owner);
      const withdrawnAmount = newBalance.sub(initialBalance);
      
      assert.equal(withdrawnAmount.toString(), contractBalance.toString(), "Withdrawn amount doesn't match contract balance");
    });
  });
});