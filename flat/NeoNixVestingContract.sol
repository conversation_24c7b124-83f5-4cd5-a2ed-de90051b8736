// File: @openzeppelin/contracts/utils/Context.sol

// SPDX-License-Identifier: MIT
// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)

pragma solidity ^0.8.0;

/**
 * @dev Provides information about the current execution context, including the
 * sender of the transaction and its data. While these are generally available
 * via msg.sender and msg.data, they should not be accessed in such a direct
 * manner, since when dealing with meta-transactions the account sending and
 * paying for execution may not be the actual sender (as far as an application
 * is concerned).
 *
 * This contract is only required for intermediate, library-like contracts.
 */
abstract contract Context {
    function _msgSender() internal view virtual returns (address) {
        return msg.sender;
    }

    function _msgData() internal view virtual returns (bytes calldata) {
        return msg.data;
    }
}

// File: @openzeppelin/contracts/access/Ownable.sol


// OpenZeppelin Contracts (last updated v4.7.0) (access/Ownable.sol)

pragma solidity ^0.8.0;

/**
 * @dev Contract module which provides a basic access control mechanism, where
 * there is an account (an owner) that can be granted exclusive access to
 * specific functions.
 *
 * By default, the owner account will be the one that deploys the contract. This
 * can later be changed with {transferOwnership}.
 *
 * This module is used through inheritance. It will make available the modifier
 * `onlyOwner`, which can be applied to your functions to restrict their use to
 * the owner.
 */
abstract contract Ownable is Context {
    address private _owner;

    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);

    /**
     * @dev Initializes the contract setting the deployer as the initial owner.
     */
    constructor() {
        _transferOwnership(_msgSender());
    }

    /**
     * @dev Throws if called by any account other than the owner.
     */
    modifier onlyOwner() {
        _checkOwner();
        _;
    }

    /**
     * @dev Returns the address of the current owner.
     */
    function owner() public view virtual returns (address) {
        return _owner;
    }

    /**
     * @dev Throws if the sender is not the owner.
     */
    function _checkOwner() internal view virtual {
        require(owner() == _msgSender(), "Ownable: caller is not the owner");
    }

    /**
     * @dev Leaves the contract without owner. It will not be possible to call
     * `onlyOwner` functions anymore. Can only be called by the current owner.
     *
     * NOTE: Renouncing ownership will leave the contract without an owner,
     * thereby removing any functionality that is only available to the owner.
     */
    function renounceOwnership() public virtual onlyOwner {
        _transferOwnership(address(0));
    }

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * Can only be called by the current owner.
     */
    function transferOwnership(address newOwner) public virtual onlyOwner {
        require(newOwner != address(0), "Ownable: new owner is the zero address");
        _transferOwnership(newOwner);
    }

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * Internal function without access restriction.
     */
    function _transferOwnership(address newOwner) internal virtual {
        address oldOwner = _owner;
        _owner = newOwner;
        emit OwnershipTransferred(oldOwner, newOwner);
    }
}

// File: @openzeppelin/contracts/utils/math/SafeMath.sol


// OpenZeppelin Contracts (last updated v4.6.0) (utils/math/SafeMath.sol)

pragma solidity ^0.8.0;

// CAUTION
// This version of SafeMath should only be used with Solidity 0.8 or later,
// because it relies on the compiler's built in overflow checks.

/**
 * @dev Wrappers over Solidity's arithmetic operations.
 *
 * NOTE: `SafeMath` is generally not needed starting with Solidity 0.8, since the compiler
 * now has built in overflow checking.
 */
library SafeMath {
    /**
     * @dev Returns the addition of two unsigned integers, with an overflow flag.
     *
     * _Available since v3.4._
     */
    function tryAdd(uint256 a, uint256 b) internal pure returns (bool, uint256) {
        unchecked {
            uint256 c = a + b;
            if (c < a) return (false, 0);
            return (true, c);
        }
    }

    /**
     * @dev Returns the subtraction of two unsigned integers, with an overflow flag.
     *
     * _Available since v3.4._
     */
    function trySub(uint256 a, uint256 b) internal pure returns (bool, uint256) {
        unchecked {
            if (b > a) return (false, 0);
            return (true, a - b);
        }
    }

    /**
     * @dev Returns the multiplication of two unsigned integers, with an overflow flag.
     *
     * _Available since v3.4._
     */
    function tryMul(uint256 a, uint256 b) internal pure returns (bool, uint256) {
        unchecked {
            // Gas optimization: this is cheaper than requiring 'a' not being zero, but the
            // benefit is lost if 'b' is also tested.
            // See: https://github.com/OpenZeppelin/openzeppelin-contracts/pull/522
            if (a == 0) return (true, 0);
            uint256 c = a * b;
            if (c / a != b) return (false, 0);
            return (true, c);
        }
    }

    /**
     * @dev Returns the division of two unsigned integers, with a division by zero flag.
     *
     * _Available since v3.4._
     */
    function tryDiv(uint256 a, uint256 b) internal pure returns (bool, uint256) {
        unchecked {
            if (b == 0) return (false, 0);
            return (true, a / b);
        }
    }

    /**
     * @dev Returns the remainder of dividing two unsigned integers, with a division by zero flag.
     *
     * _Available since v3.4._
     */
    function tryMod(uint256 a, uint256 b) internal pure returns (bool, uint256) {
        unchecked {
            if (b == 0) return (false, 0);
            return (true, a % b);
        }
    }

    /**
     * @dev Returns the addition of two unsigned integers, reverting on
     * overflow.
     *
     * Counterpart to Solidity's `+` operator.
     *
     * Requirements:
     *
     * - Addition cannot overflow.
     */
    function add(uint256 a, uint256 b) internal pure returns (uint256) {
        return a + b;
    }

    /**
     * @dev Returns the subtraction of two unsigned integers, reverting on
     * overflow (when the result is negative).
     *
     * Counterpart to Solidity's `-` operator.
     *
     * Requirements:
     *
     * - Subtraction cannot overflow.
     */
    function sub(uint256 a, uint256 b) internal pure returns (uint256) {
        return a - b;
    }

    /**
     * @dev Returns the multiplication of two unsigned integers, reverting on
     * overflow.
     *
     * Counterpart to Solidity's `*` operator.
     *
     * Requirements:
     *
     * - Multiplication cannot overflow.
     */
    function mul(uint256 a, uint256 b) internal pure returns (uint256) {
        return a * b;
    }

    /**
     * @dev Returns the integer division of two unsigned integers, reverting on
     * division by zero. The result is rounded towards zero.
     *
     * Counterpart to Solidity's `/` operator.
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function div(uint256 a, uint256 b) internal pure returns (uint256) {
        return a / b;
    }

    /**
     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),
     * reverting when dividing by zero.
     *
     * Counterpart to Solidity's `%` operator. This function uses a `revert`
     * opcode (which leaves remaining gas untouched) while Solidity uses an
     * invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function mod(uint256 a, uint256 b) internal pure returns (uint256) {
        return a % b;
    }

    /**
     * @dev Returns the subtraction of two unsigned integers, reverting with custom message on
     * overflow (when the result is negative).
     *
     * CAUTION: This function is deprecated because it requires allocating memory for the error
     * message unnecessarily. For custom revert reasons use {trySub}.
     *
     * Counterpart to Solidity's `-` operator.
     *
     * Requirements:
     *
     * - Subtraction cannot overflow.
     */
    function sub(
        uint256 a,
        uint256 b,
        string memory errorMessage
    ) internal pure returns (uint256) {
        unchecked {
            require(b <= a, errorMessage);
            return a - b;
        }
    }

    /**
     * @dev Returns the integer division of two unsigned integers, reverting with custom message on
     * division by zero. The result is rounded towards zero.
     *
     * Counterpart to Solidity's `/` operator. Note: this function uses a
     * `revert` opcode (which leaves remaining gas untouched) while Solidity
     * uses an invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function div(
        uint256 a,
        uint256 b,
        string memory errorMessage
    ) internal pure returns (uint256) {
        unchecked {
            require(b > 0, errorMessage);
            return a / b;
        }
    }

    /**
     * @dev Returns the remainder of dividing two unsigned integers. (unsigned integer modulo),
     * reverting with custom message when dividing by zero.
     *
     * CAUTION: This function is deprecated because it requires allocating memory for the error
     * message unnecessarily. For custom revert reasons use {tryMod}.
     *
     * Counterpart to Solidity's `%` operator. This function uses a `revert`
     * opcode (which leaves remaining gas untouched) while Solidity uses an
     * invalid opcode to revert (consuming all remaining gas).
     *
     * Requirements:
     *
     * - The divisor cannot be zero.
     */
    function mod(
        uint256 a,
        uint256 b,
        string memory errorMessage
    ) internal pure returns (uint256) {
        unchecked {
            require(b > 0, errorMessage);
            return a % b;
        }
    }
}

// File: contracts/NeoNixVestingContract.sol


pragma solidity ^0.8.0;


contract NeoNixVestingContract is Ownable {
    using SafeMath for uint256;
    
    enum VestingCategory {
        ICO_INVESTORS,
        TEAM_ADVISORS,
        PARTNERSHIPS,
        EARLY_ADOPTERS,
        VALIDATORS_REWARDS,
        MARKETING_EXPANSION,
        TREASURY_RESERVE,
        LIQUIDITY_POOL
    }
    
    struct VestingSchedule {
        uint256 totalAmount;
        uint256 startTimestamp;
        uint256 cliffDuration;
        uint256 vestingDuration;
        uint256 monthlyReleasePercentage; // In basis points (1/100 of a percent)
        uint256 releasedAmount;
        bool isFlexible;
    }
    
    mapping(address => VestingSchedule) public vestingSchedules;
    mapping(address => VestingCategory) public addressCategory;
    mapping(VestingCategory => address) public categoryAddress;
    
    event VestingScheduleCreated(address beneficiary, VestingCategory category, uint256 amount);
    event TokensReleased(address beneficiary, uint256 amount);
    event FlexibleVestingReleased(address beneficiary, uint256 amount);
    event FundsDeposited(address sender, uint256 amount);
    
    // Allow contract to receive native coin
    receive() external payable {
        emit FundsDeposited(msg.sender, msg.value);
    }
    
    function setCategoryAddress(VestingCategory _category, address _beneficiary) external onlyOwner {
        require(_beneficiary != address(0), "Invalid address");
        categoryAddress[_category] = _beneficiary;
        addressCategory[_beneficiary] = _category;
    }
    
    function createVestingSchedule(
        address _beneficiary,
        uint256 _amount,
        uint256 _startTimestamp,
        bool _isFlexible
    ) external onlyOwner {
        require(_beneficiary != address(0), "Invalid beneficiary address");
        require(_amount > 0, "Amount must be greater than 0");
        require(vestingSchedules[_beneficiary].totalAmount == 0, "Vesting schedule already exists");
        require(address(this).balance >= _amount, "Insufficient contract balance");
        
        VestingCategory category = addressCategory[_beneficiary];
        
        uint256 cliffDuration;
        uint256 vestingDuration;
        uint256 monthlyReleasePercentage;
        
        if (_isFlexible) {
            // Flexible schedules have no predefined parameters
            vestingSchedules[_beneficiary] = VestingSchedule({
                totalAmount: _amount,
                startTimestamp: _startTimestamp,
                cliffDuration: 0,
                vestingDuration: 0,
                monthlyReleasePercentage: 0,
                releasedAmount: 0,
                isFlexible: true
            });
        } else {
            // Set parameters based on category
            if (category == VestingCategory.ICO_INVESTORS) {
                cliffDuration = 0;
                vestingDuration = 36 * 30 days;
                monthlyReleasePercentage = 278; // 2.78% per month (100/36)
            } else if (category == VestingCategory.TEAM_ADVISORS) {
                cliffDuration = 60 * 30 days;
                vestingDuration = 60 * 30 days;
                monthlyReleasePercentage = 167; // 1.67% per month
            } else if (category == VestingCategory.PARTNERSHIPS) {
                cliffDuration = 12 * 30 days;
                vestingDuration = 36 * 30 days;
                monthlyReleasePercentage = 277; // 2.77% per month
            } else if (category == VestingCategory.MARKETING_EXPANSION) {
                cliffDuration = 24 * 30 days;
                vestingDuration = 48 * 30 days;
                monthlyReleasePercentage = 208; // 2.08% per month
            } else {
                revert("Invalid category for fixed vesting");
            }
            
            vestingSchedules[_beneficiary] = VestingSchedule({
                totalAmount: _amount,
                startTimestamp: _startTimestamp,
                cliffDuration: cliffDuration,
                vestingDuration: vestingDuration,
                monthlyReleasePercentage: monthlyReleasePercentage,
                releasedAmount: 0,
                isFlexible: false
            });
        }
        
        emit VestingScheduleCreated(_beneficiary, category, _amount);
    }
    
    function claimVest() external {
        address beneficiary = msg.sender;
        VestingSchedule storage schedule = vestingSchedules[beneficiary];
        
        require(schedule.totalAmount > 0, "No vesting schedule found");
        
        if (schedule.isFlexible) {
            revert("Flexible vesting requires owner approval");
        }
        
        uint256 releasableAmount = calculateReleasableAmount(beneficiary);
        require(releasableAmount > 0, "No tokens available for release");
        require(address(this).balance >= releasableAmount, "Insufficient contract balance");
        
        schedule.releasedAmount = schedule.releasedAmount.add(releasableAmount);
        
        (bool success, ) = payable(beneficiary).call{value: releasableAmount}("");
        require(success, "Native coin transfer failed");
        
        emit TokensReleased(beneficiary, releasableAmount);
    }
    
    function releaseFlexibleVesting(address _beneficiary, uint256 _amount) external onlyOwner {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        
        require(schedule.isFlexible, "Not a flexible vesting schedule");
        require(_amount > 0, "Amount must be greater than 0");
        require(schedule.releasedAmount.add(_amount) <= schedule.totalAmount, "Exceeds available tokens");
        require(address(this).balance >= _amount, "Insufficient contract balance");
        
        schedule.releasedAmount = schedule.releasedAmount.add(_amount);
        
        (bool success, ) = payable(_beneficiary).call{value: _amount}("");
        require(success, "Native coin transfer failed");
        
        emit FlexibleVestingReleased(_beneficiary, _amount);
    }
    
    function calculateReleasableAmount(address _beneficiary) public view returns (uint256) {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        
        if (schedule.totalAmount == 0 || schedule.isFlexible) {
            return 0;
        }
        
        if (block.timestamp < schedule.startTimestamp.add(schedule.cliffDuration)) {
            return 0;
        }
        
        if (schedule.releasedAmount >= schedule.totalAmount) {
            return 0;
        }
        
        uint256 elapsedTime = block.timestamp.sub(schedule.startTimestamp);
        uint256 elapsedMonths = elapsedTime.div(30 days);
        
        uint256 vestedAmount;
        
        if (elapsedTime >= schedule.vestingDuration) {
            vestedAmount = schedule.totalAmount;
        } else {
            vestedAmount = schedule.totalAmount.mul(schedule.monthlyReleasePercentage).mul(elapsedMonths).div(10000);
        }
        
        uint256 releasableAmount = vestedAmount.sub(schedule.releasedAmount);
        
        return releasableAmount;
    }
    
    function getVestingSchedule(address _beneficiary) external view returns (
        uint256 totalAmount,
        uint256 startTimestamp,
        uint256 cliffDuration,
        uint256 vestingDuration,
        uint256 monthlyReleasePercentage,
        uint256 releasedAmount,
        bool isFlexible
    ) {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        return (
            schedule.totalAmount,
            schedule.startTimestamp,
            schedule.cliffDuration,
            schedule.vestingDuration,
            schedule.monthlyReleasePercentage,
            schedule.releasedAmount,
            schedule.isFlexible
        );
    }
    
    function getContractBalance() external view returns (uint256) {
        return address(this).balance;
    }
    
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");
        
        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Native coin transfer failed");
    }
    
    // Function to deposit funds to the contract
    function depositFunds() external payable onlyOwner {
        require(msg.value > 0, "Must send some funds");
        emit FundsDeposited(msg.sender, msg.value);
    }
}
